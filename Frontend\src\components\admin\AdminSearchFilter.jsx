import React from "react";
import { FaSearch, FaFilter } from "react-icons/fa";
import "../../styles/AdminSearchFilter.css";

const AdminSearchFilter = ({
  searchTerm,
  onSearchChange,
  searchPlaceholder = "Search...",
  filters = [],
  className = "",
  showSearch = true,
  showFilters = true,
  children
}) => {
  return (
    <div className={`admin-search-filter ${className}`}>
      <div className="controls-section">
        {showSearch && (
          <div className="search-box">
            <FaSearch className="search-icon" />
            <input
              type="text"
              placeholder={searchPlaceholder}
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
            />
          </div>
        )}

        {showFilters && filters.map((filter, index) => (
          <div key={index} className="filter-box">
            <FaFilter className="filter-icon" />
            <select
              value={filter.value}
              onChange={(e) => filter.onChange(e.target.value)}
            >
              {filter.options.map((option, optIndex) => (
                <option key={optIndex} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        ))}

        {children}
      </div>
    </div>
  );
};

export default AdminSearchFilter;
