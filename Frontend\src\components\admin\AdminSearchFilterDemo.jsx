import React, { useState } from "react";
import AdminSearchFilter from "./AdminSearchFilter";
import "../../styles/AdminSearchFilter.css";

const AdminSearchFilterDemo = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");

  return (
    <div style={{ padding: "20px", backgroundColor: "#f8f9fa" }}>
      <h2 style={{ marginBottom: "30px", color: "#333" }}>
        Admin Search & Filter Component Demo
      </h2>
      
      <div style={{ marginBottom: "40px" }}>
        <h3 style={{ marginBottom: "15px", color: "#666" }}>
          User Management Style
        </h3>
        <AdminSearchFilter
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          searchPlaceholder="Search users by name or email..."
          filters={[
            {
              value: statusFilter,
              onChange: setStatusFilter,
              options: [
                { value: "all", label: "All Roles" },
                { value: "buyer", label: "Buyers" },
                { value: "seller", label: "Sellers" },
                { value: "admin", label: "Admins" }
              ]
            },
            {
              value: categoryFilter,
              onChange: setCategoryFilter,
              options: [
                { value: "all", label: "All Status" },
                { value: "active", label: "Active" },
                { value: "inactive", label: "Inactive" }
              ]
            }
          ]}
          className="demo-search-filter"
        />
      </div>

      <div style={{ marginBottom: "40px" }}>
        <h3 style={{ marginBottom: "15px", color: "#666" }}>
          Content Management Style
        </h3>
        <AdminSearchFilter
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          searchPlaceholder="Search content by title or seller..."
          filters={[
            {
              value: categoryFilter,
              onChange: setCategoryFilter,
              options: [
                { value: "all", label: "All Categories" },
                { value: "football", label: "Football" },
                { value: "basketball", label: "Basketball" },
                { value: "baseball", label: "Baseball" }
              ]
            },
            {
              value: statusFilter,
              onChange: setStatusFilter,
              options: [
                { value: "all", label: "All Status" },
                { value: "Published", label: "Published" },
                { value: "Draft", label: "Draft" }
              ]
            }
          ]}
          className="demo-search-filter"
        />
      </div>

      <div style={{ marginBottom: "40px" }}>
        <h3 style={{ marginBottom: "15px", color: "#666" }}>
          Single Filter Style (Bid Management)
        </h3>
        <AdminSearchFilter
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          searchPlaceholder="Search bids by ID, content, or bidder..."
          filters={[
            {
              value: statusFilter,
              onChange: setStatusFilter,
              options: [
                { value: "all", label: "All Status" },
                { value: "Active", label: "Active" },
                { value: "Won", label: "Won" },
                { value: "Lost", label: "Lost" },
                { value: "Outbid", label: "Outbid" },
                { value: "Cancelled", label: "Cancelled" }
              ]
            }
          ]}
          className="demo-search-filter"
        />
      </div>

      <div style={{ marginBottom: "40px" }}>
        <h3 style={{ marginBottom: "15px", color: "#666" }}>
          Search Only Style
        </h3>
        <AdminSearchFilter
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          searchPlaceholder="Search anything..."
          filters={[]}
          className="demo-search-filter"
        />
      </div>

      <div style={{ 
        backgroundColor: "#fff", 
        padding: "20px", 
        borderRadius: "8px", 
        border: "1px solid #e0e0e0",
        marginTop: "30px"
      }}>
        <h3 style={{ marginBottom: "15px", color: "#333" }}>
          Styling Features:
        </h3>
        <ul style={{ color: "#666", lineHeight: "1.6" }}>
          <li><strong>Icon Color:</strong> #666 (consistent gray)</li>
          <li><strong>Font Size:</strong> 14px for icons and text</li>
          <li><strong>Padding:</strong> 8px 12px for containers, 5px for inputs</li>
          <li><strong>Gap:</strong> 20px between search and filter elements, 10px within elements</li>
          <li><strong>Border:</strong> 1px solid #e0e0e0 with 6px border radius</li>
          <li><strong>Focus State:</strong> Blue border (#007bff) with subtle shadow</li>
          <li><strong>Responsive:</strong> Stacks vertically on mobile devices</li>
          <li><strong>Flexible:</strong> Supports multiple filters and search-only modes</li>
        </ul>
      </div>
    </div>
  );
};

export default AdminSearchFilterDemo;
