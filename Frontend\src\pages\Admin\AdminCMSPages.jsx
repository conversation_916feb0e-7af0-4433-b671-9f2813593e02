import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectCMSPages,
  selectSelectedCMSPages,
  selectUI,
  setSelectedCMSPages,
  showCMSEditorModal,
  updateCMSPage,
  deleteCMSPage,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import AdminLayout from "../../components/admin/AdminLayout";
import CMSEditorModal from "../../components/admin/CMSEditorModal";
import Table from "../../components/common/Table";
import AdminTableActions from "../../components/admin/AdminTableActions";
import AdminSearchFilter from "../../components/admin/AdminSearchFilter";
import "../../styles/AdminCMSPages.css";

// Icons
import { FaFileAlt, FaSearch, FaEdit, FaTrash, FaEye, FaToggleOn, FaToggleOff } from "react-icons/fa";
import { MdAdd, MdPublish } from "react-icons/md";

const AdminCMSPages = () => {
  const dispatch = useDispatch();
  const cmsPages = useSelector(selectCMSPages);
  const selectedPages = useSelector(selectSelectedCMSPages);
  const ui = useSelector(selectUI);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Filter pages based on search and filters
  const filteredPages = cmsPages.filter(page => {
    const matchesSearch =
      page.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      page.slug.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === "all" || page.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Handle select all
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      dispatch(setSelectedCMSPages(filteredPages.map(page => page.id)));
    } else {
      dispatch(setSelectedCMSPages([]));
    }
  };

  // Handle individual select
  const handleSelectPage = (pageId) => {
    const newSelection = selectedPages.includes(pageId)
      ? selectedPages.filter(id => id !== pageId)
      : [...selectedPages, pageId];
    dispatch(setSelectedCMSPages(newSelection));
  };

  // Handle page actions
  const handlePageAction = (page, action) => {
    switch (action) {
      case 'view':
        window.open(`/cms/${page.slug}`, '_blank');
        break;
      case 'edit':
        dispatch(showCMSEditorModal(page));
        break;
      case 'delete':
        if (window.confirm(`Delete page "${page.title}"? This action cannot be undone.`)) {
          dispatch(deleteCMSPage(page.id));
          dispatch(addActivity({
            id: Date.now(),
            type: 'cms_deletion',
            description: `CMS page deleted: ${page.title}`,
            timestamp: new Date().toISOString(),
            user: 'Admin',
          }));
          alert(`Page "${page.title}" has been deleted!`);
        }
        break;
      case 'toggle':
        const newStatus = page.status === 'published' ? 'draft' : 'published';
        dispatch(updateCMSPage({ ...page, status: newStatus }));
        dispatch(addActivity({
          id: Date.now(),
          type: 'cms_status_change',
          description: `CMS page ${newStatus}: ${page.title}`,
          timestamp: new Date().toISOString(),
          user: 'Admin',
        }));
        alert(`Page "${page.title}" has been ${newStatus}!`);
        break;
      default:
        break;
    }
  };

  // Handle bulk actions
  const handleBulkAction = (action) => {
    if (selectedPages.length === 0) {
      alert('Please select pages first');
      return;
    }

    switch (action) {
      case 'publish':
        if (window.confirm(`Publish ${selectedPages.length} selected pages?`)) {
          selectedPages.forEach(id => {
            const page = cmsPages.find(p => p.id === id);
            if (page) {
              dispatch(updateCMSPage({ ...page, status: 'published' }));
            }
          });
          dispatch(addActivity({
            id: Date.now(),
            type: 'bulk_cms_publish',
            description: `Bulk published ${selectedPages.length} CMS pages`,
            timestamp: new Date().toISOString(),
            user: 'Admin',
          }));
          alert(`${selectedPages.length} pages published`);
          dispatch(setSelectedCMSPages([]));
        }
        break;
      case 'unpublish':
        if (window.confirm(`Unpublish ${selectedPages.length} selected pages?`)) {
          selectedPages.forEach(id => {
            const page = cmsPages.find(p => p.id === id);
            if (page) {
              dispatch(updateCMSPage({ ...page, status: 'draft' }));
            }
          });
          dispatch(addActivity({
            id: Date.now(),
            type: 'bulk_cms_unpublish',
            description: `Bulk unpublished ${selectedPages.length} CMS pages`,
            timestamp: new Date().toISOString(),
            user: 'Admin',
          }));
          alert(`${selectedPages.length} pages unpublished`);
          dispatch(setSelectedCMSPages([]));
        }
        break;
      case 'delete':
        if (window.confirm(`Delete ${selectedPages.length} selected pages? This action cannot be undone.`)) {
          selectedPages.forEach(id => {
            dispatch(deleteCMSPage(id));
          });
          dispatch(addActivity({
            id: Date.now(),
            type: 'bulk_cms_deletion',
            description: `Bulk deleted ${selectedPages.length} CMS pages`,
            timestamp: new Date().toISOString(),
            user: 'Admin',
          }));
          alert(`${selectedPages.length} pages deleted`);
          dispatch(setSelectedCMSPages([]));
        }
        break;
      default:
        break;
    }
  };

  // Handle create new page
  const handleCreateNewPage = () => {
    dispatch(showCMSEditorModal(null)); // null means create new page
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get status badge class
  const getStatusBadge = (status) => {
    switch (status) {
      case 'published':
        return 'status-badge published';
      case 'draft':
        return 'status-badge draft';
      default:
        return 'status-badge';
    }
  };

  // Table configuration
  const tableColumns = [
    {
      key: 'select',
      label: (
        <input
          type="checkbox"
          onChange={handleSelectAll}
          checked={selectedPages.length === filteredPages.length && filteredPages.length > 0}
        />
      ),
      render: (page) => (
        <input
          type="checkbox"
          checked={selectedPages.includes(page.id)}
          onChange={() => handleSelectPage(page.id)}
        />
      ),
      className: 'select-column'
    },
    {
      key: 'page',
      label: 'Page Title',
      render: (page) => (
        <div className="page-info">
          <div className="page-icon">
            <FaFileAlt />
          </div>
          <div className="page-details">
            <span className="page-title">{page.title}</span>
          </div>
        </div>
      )
    },
    {
      key: 'slug',
      label: 'URL Slug',
      render: (page) => (
        <code className="url-slug">/{page.slug}</code>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (page) => (
        <div className="status-controls">
          <span className={getStatusBadge(page.status)}>
            {page.status}
          </span>
          <button
            className="toggle-btn"
            onClick={() => {
              const newStatus = page.status === 'published' ? 'draft' : 'published';
              dispatch(updateCMSPage({ ...page, status: newStatus }));
              dispatch(addActivity({
                id: Date.now(),
                type: 'cms_status_change',
                description: `CMS page ${newStatus}: ${page.title}`,
                timestamp: new Date().toISOString(),
                user: 'Admin',
              }));
            }}
            title={`${page.status === 'published' ? 'Unpublish' : 'Publish'} page`}
          >
            {page.status === 'published' ? <FaToggleOn /> : <FaToggleOff />}
          </button>
        </div>
      )
    },
    {
      key: 'lastModified',
      label: 'Last Modified',
      render: (page) => formatDate(page.lastModified)
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (page) => (
        <AdminTableActions
          item={page}
          onView={() => handlePageAction(page, 'view')}
          onEdit={() => handlePageAction(page, 'edit')}
          onDelete={() => handlePageAction(page, 'delete')}
          tooltips={{
            view: 'View Page',
            edit: 'Edit Page',
            delete: 'Delete Page'
          }}
        />
      ),
      className: 'actions-column'
    }
  ];

  return (
    <AdminLayout>
      <div className="AdminCMSPages">
<div className="flex space-between gap-10">
          <div className="AdminUserManagement__main">
          {/* Header Actions */}
          <div className="AdminCMSPages__header">
            <div className="header-left">
              <div className="search-container">
                <FaSearch className="search-icon" />
                <input
                  type="text"
                  placeholder="Search pages by title or slug..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                />
              </div>
            </div>

          
          </div>

          {/* Filters */}
          <div className="AdminCMSPages__filters">
            <div className="filter-group">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="filter-select"
              >
                <option value="all">All Status</option>
                <option value="published">Published</option>
                <option value="draft">Draft</option>
              </select>
            </div>

            {selectedPages.length > 0 && (
              <div className="bulk-actions">
                <span className="selected-count">
                  {selectedPages.length} selected
                </span>
                <button
                  className="btn btn-success"
                  onClick={() => handleBulkAction('publish')}
                >
                  <MdPublish />
                  Publish
                </button>
                <button
                  className="btn btn-outline"
                  onClick={() => handleBulkAction('unpublish')}
                >
                  Unpublish
                </button>
                <button
                  className="btn btn-danger"
                  onClick={() => handleBulkAction('delete')}
                >
                  Delete
                </button>
              </div>
            )}
          </div>

        </div>
  <div className="header-right">
              <button
                className="btn btn-primary"
                onClick={handleCreateNewPage}
              >
                <MdAdd />
                Create New Page
              </button>
            </div>
</div>
        {/* Pages Table */}
        <div className="AdminCMSPages__table">
          <Table
            columns={tableColumns}
            data={filteredPages}
            isAdmin={true}
            loading={{
              isLoading: ui.loading?.cmsPages,
              message: "Loading pages..."
            }}
            emptyMessage={
              <div className="no-results">
                <FaFileAlt className="no-results-icon" />
                <h3>No pages found</h3>
                <p>Try adjusting your search or filter criteria</p>
              </div>
            }
            className="pages-table"
          />
        </div>
        {/* Pagination */}
        <div className="AdminCMSPages__pagination">
          <div className="pagination-info">
            Showing {filteredPages.length} of {cmsPages.length} pages
          </div>
          <div className="pagination-controls">
            <button className="btn btn-outline" disabled>Previous</button>
            <span className="page-number active">1</span>
            <button className="btn btn-outline" disabled>Next</button>
          </div>
        </div>

        {/* Modals */}
        {ui.showCMSEditorModal && <CMSEditorModal />}
      </div>
    </AdminLayout>
  );
};

export default AdminCMSPages;
