import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import AdminLayout from "../../components/admin/AdminLayout";
import AdminSearchFilter from "../../components/admin/AdminSearchFilter";
import "../../styles/AdminRequestManagement.css";
import { FaEye, FaEdit, FaTrash, FaCheck, FaTimes } from "react-icons/fa";

const AdminRequestManagement = () => {
    const dispatch = useDispatch();
    const [searchTerm, setSearchTerm] = useState("");
    const [statusFilter, setStatusFilter] = useState("all");

    return (
        <AdminLayout>
            <div className="AdminRequestManagement">
                <div className="AdminRequestManagement__header">
                
                    <p>Manage and monitor all custom training requests</p>
                </div>

                {/* Search and Filter Section */}
                <AdminSearchFilter
                    searchTerm={searchTerm}
                    onSearchChange={setSearchTerm}
                    searchPlaceholder="Search requests..."
                    filters={[
                        {
                            value: statusFilter,
                            onChange: setStatusFilter,
                            options: [
                                { value: "all", label: "All Status" },
                                { value: "pending", label: "Pending" },
                                { value: "approved", label: "Approved" },
                                { value: "rejected", label: "Rejected" },
                                { value: "completed", label: "Completed" }
                            ]
                        }
                    ]}
                    className="AdminRequestManagement__search-filter"
                />

                {/* Table Section */}
                <div className="AdminRequestManagement__table">
                    <table>
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" />
                                </th>
                                <th>Request ID</th>
                                <th>Title</th>
                                <th>Buyer</th>
                                <th>Sport</th>
                                <th>Budget</th>
                                <th>Status</th>
                                <th>Created Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {/* Table rows will be populated here */}
                            <tr>
                                <td colSpan="9" className="no-data">
                                    Loading requests...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                {/* Pagination Section */}
                <div className="AdminRequestManagement__pagination">
                    <button disabled>Previous</button>
                    <span>Page 1 of 1</span>
                    <button disabled>Next</button>
                </div>
            </div>
        </AdminLayout>
    );
};

export default AdminRequestManagement; 