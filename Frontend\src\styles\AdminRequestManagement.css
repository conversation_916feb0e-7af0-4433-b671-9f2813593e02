

.AdminRequestManagement .AdminRequestManagement__header {
  margin-bottom: 0.5rem;
}

.AdminRequestManagement .AdminRequestManagement__header h2 {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.AdminRequestManagement .AdminRequestManagement__header p {
  color: #666;
  font-size: 1rem;
}

.AdminRequestManagement .AdminRequestManagement__controls {
  display: flex;
 
  align-items: center;
  margin-bottom: 24px;
  gap: 24px;
}

.AdminRequestManagement .search-box {
  flex: 1;
  position: relative;
  max-width: 400px;
}

.AdminRequestManagement .search-box input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
}

.AdminRequestManagement .search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.AdminRequestManagement .filter-box {
  display: flex;
  gap: 1rem;
}

.AdminRequestManagement .filter-box select {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  min-width: 150px;
}

.AdminRequestManagement .AdminRequestManagement__table {
  background: white;
  border-radius: 8px;
border: 1px solid var(--light-gray);
  overflow: hidden;
  margin-bottom: 2rem;
}

.AdminRequestManagement .AdminRequestManagement__table table {
  width: 100%;
  border-collapse: collapse;
}

.AdminRequestManagement .AdminRequestManagement__table th,
.AdminRequestManagement .AdminRequestManagement__table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.AdminRequestManagement .AdminRequestManagement__table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.AdminRequestManagement .AdminRequestManagement__table tr:hover {
  background: #f8f9fa;
}

.AdminRequestManagement .AdminRequestManagement__table .no-data {
  text-align: center;
  color: #666;
  padding: 2rem;
}

.AdminRequestManagement .AdminRequestManagement__pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

.AdminRequestManagement .AdminRequestManagement__pagination button {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.AdminRequestManagement .AdminRequestManagement__pagination button:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.AdminRequestManagement .AdminRequestManagement__pagination span {
  color: #666;
}

/* Status badges */
.AdminRequestManagement .status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.AdminRequestManagement .status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.AdminRequestManagement .status-badge.approved {
  background: #d4edda;
  color: #155724;
}

.AdminRequestManagement .status-badge.rejected {
  background: #f8d7da;
  color: #721c24;
}

.AdminRequestManagement .status-badge.completed {
  background: #cce5ff;
  color: #004085;
}

/* Action buttons */
.AdminRequestManagement .action-buttons {
  display: flex;
  gap: 0.5rem;
}

.AdminRequestManagement .action-button {
  padding: 0.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.AdminRequestManagement .action-button.view {
  background: #007bff;
}

.AdminRequestManagement .action-button.edit {
  background: #28a745;
}

.AdminRequestManagement .action-button.delete {
  background: #dc3545;
}

.AdminRequestManagement .action-button:hover {
  opacity: 0.9;
}
