/* Admin Search Filter Component Styles */
.admin-search-filter {
  margin-bottom: 20px;
}

/* Controls Section */
.admin-search-filter .controls-section {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: center;
  flex-wrap: wrap;
}

/* Search Box */
.admin-search-filter .search-box {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #fff;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  min-width: 250px;
  flex: 1;
  max-width: 400px;
  position: relative;
}

.admin-search-filter .search-box:focus-within {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.admin-search-filter .search-icon {
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
}

.admin-search-filter .search-box input {
  border: none;
  outline: none;
  padding: 5px;
  width: 100%;
  font-size: 14px;
  background: transparent;
}

.admin-search-filter .search-box input::placeholder {
  color: #999;
}

/* Filter Box */
.admin-search-filter .filter-box {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #fff;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  min-width: 150px;
}

.admin-search-filter .filter-box:focus-within {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.admin-search-filter .filter-icon {
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
}

.admin-search-filter .filter-box select {
  border: none;
  outline: none;
  padding: 5px;
  min-width: 120px;
  font-size: 14px;
  background: transparent;
  cursor: pointer;
}

.admin-search-filter .filter-box select:focus {
  outline: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-search-filter .controls-section {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .admin-search-filter .search-box,
  .admin-search-filter .filter-box {
    min-width: unset;
    max-width: unset;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .admin-search-filter .controls-section {
    gap: 10px;
  }

  .admin-search-filter .search-box,
  .admin-search-filter .filter-box {
    padding: 10px 12px;
  }

  .admin-search-filter .search-icon,
  .admin-search-filter .filter-icon {
    font-size: 16px;
  }

  .admin-search-filter .search-box input,
  .admin-search-filter .filter-box select {
    font-size: 16px;
    padding: 6px;
  }
}
