/* AdminSettings Component Styles */
.AdminSettings {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Settings Navigation */
.AdminSettings .AdminSettings__nav {
  display: flex;
  gap: 4px;
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  padding: 4px;
  
}

.AdminSettings .nav-tab {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  background: none;
  color: var(--secondary-color);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  justify-content: center;
}

.AdminSettings .nav-tab:hover {
  background-color: var(--bg-gray);
  color: var(--btn-color);
}

.AdminSettings .nav-tab.active {
  background-color: var(--btn-color);
  color: var(--white);
  font-weight: 600;
}

/* Settings Content */
.AdminSettings .AdminSettings__content {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.AdminSettings .settings-section {
  padding: var(--heading6);
}

.AdminSettings .section-header {
  margin-bottom: var(--heading6);
  padding-bottom: var(--basefont);
  border-bottom: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.AdminSettings .section-header h3 {
  margin: 0 0 4px 0;
  font-size: var(--heading5);
  color: var(--secondary-color);
}

.AdminSettings .section-header p {
  margin: 0;
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

/* Form Styles */
.AdminSettings .settings-form {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

.AdminSettings .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
}

.AdminSettings .form-group {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.AdminSettings .form-group label {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
}

.AdminSettings .form-input,
.AdminSettings .form-textarea {
  padding: var(--smallfont) var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  background-color: var(--white);
  transition: all 0.3s ease;
}

.AdminSettings .form-input:focus,
.AdminSettings .form-textarea:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
}

.AdminSettings .form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* Logo Upload */
.AdminSettings .logo-upload {
  display: flex;
  align-items: center;
  gap: var(--basefont);
}

.AdminSettings .logo-preview {
  width: 120px;
  height: 80px;
  border: 2px dashed var(--light-gray);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.AdminSettings .logo-preview img {
  width: 120px;
  height: 80px;
  object-fit: contain;
  object-position: center;
}

.AdminSettings .logo-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: var(--dark-gray);
  font-size: var(--extrasmallfont);
}

.AdminSettings .logo-placeholder svg {
  font-size: var(--heading6);
}

/* Notification Groups */
.AdminSettings .notification-group,
.AdminSettings .security-group {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.AdminSettings .notification-group h4,
.AdminSettings .security-group h4 {
  margin: 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
  padding-bottom: var(--smallfont);
  border-bottom: 1px solid var(--bg-gray);
}

/* Toggle Settings */
.AdminSettings .toggle-setting {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.AdminSettings .toggle-setting:hover {
  border-color: var(--btn-color);
  box-shadow: var(--box-shadow-light);
}

.AdminSettings .toggle-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.AdminSettings .toggle-label {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
}

.AdminSettings .toggle-description {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* Toggle Switch */
.AdminSettings .toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.AdminSettings .toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.AdminSettings .toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--light-gray);
  transition: 0.3s;
  border-radius: 24px;
}

.AdminSettings .toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: var(--white);
  transition: 0.3s;
  border-radius: 50%;
}

.AdminSettings .toggle-switch input:checked + .toggle-slider {
  background-color: var(--btn-color);
}

.AdminSettings .toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* Roles Grid */
.AdminSettings .roles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--basefont);
}

.AdminSettings .role-card {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--basefont);
  transition: all 0.3s ease;
}

.AdminSettings .role-card:hover {
  border-color: var(--btn-color);
  box-shadow: var(--box-shadow-light);
}

.AdminSettings .role-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--basefont);
}

.AdminSettings .role-header h4 {
  margin: 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
}

.AdminSettings .role-count {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  background-color: var(--bg-gray);
  padding: 2px 8px;
  border-radius: 12px;
}

.AdminSettings .role-permissions {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: var(--basefont);
}

.AdminSettings .permission-tag {
  font-size: var(--extrasmallfont);
  background-color: var(--bg-blue);
  color: var(--btn-color);
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 500;
}

/* Buttons */
.AdminSettings .btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.AdminSettings .btn.btn-primary {
  background-color: var(--btn-color);
  color: var(--white);
}

.AdminSettings .btn.btn-primary:hover {
  background-color: #d32f2f;
}

.AdminSettings .btn.btn-outline {
  background-color: transparent;
  color: var(--secondary-color);
  border: 1px solid var(--light-gray);
}

.AdminSettings .btn.btn-outline:hover {
  background-color: var(--bg-gray);
}

/* Settings Actions */
.AdminSettings .settings-actions {
  padding: 0 var(--heading6) var(--heading6) var(--heading6);
 

  display: flex;
  justify-content: flex-end;
}

/* Responsive styles */
@media (max-width: 768px) {
  .AdminSettings .AdminSettings__nav {
    flex-direction: column;
  }

  .AdminSettings .nav-tab {
    justify-content: flex-start;
  }

  .AdminSettings .form-row {
    grid-template-columns: 1fr;
  }

  .AdminSettings .logo-upload {
    flex-direction: column;
    align-items: flex-start;
  }

  .AdminSettings .toggle-setting {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--smallfont);
  }

  .AdminSettings .roles-grid {
    grid-template-columns: 1fr;
  }

  .AdminSettings .settings-actions {
    padding: var(--basefont);
  }
}

@media (max-width: 480px) {
  .AdminSettings .AdminSettings__nav {
    padding: 2px;
  }

  .AdminSettings .nav-tab {
    padding: var(--smallfont);
    font-size: var(--extrasmallfont);
  }

  .AdminSettings .nav-tab svg {
    display: none;
  }

  .AdminSettings .settings-section {
    padding: var(--basefont);
  }

  .AdminSettings .role-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
